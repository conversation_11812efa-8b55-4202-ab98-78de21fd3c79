// Fabric.js integration utilities for AI-generated content
import { fabric } from "fabric"

// Get Fabric instance (for compatibility with different versions)
export const getFabric = async () => {
  return fabric
}

// Utility to safely check if canvas is available and valid
export const isCanvasAvailable = (canvas) => {
  return canvas && typeof canvas.getActiveObject === 'function'
}

// Utility to safely get active object
export const getActiveObject = (canvas) => {
  if (!isCanvasAvailable(canvas)) {
    return null
  }

  try {
    return canvas.getActiveObject()
  } catch (error) {
    console.error('Error getting active object:', error)
    return null
  }
}

// Add AI-generated image to canvas
export const addImageToCanvas = async (canvas, imageUrl, options = {}) => {
  if (!isCanvasAvailable(canvas)) {
    console.error('Canvas is not available for addImageToCanvas')
    return null
  }

  if (!imageUrl) {
    console.error('Image URL is required for addImageToCanvas')
    return null
  }

  try {
    // Add AI-generated image to canvas

    // Use Promise-based approach with fabric.Image.fromURL
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(imageUrl, (image) => {
        if (!image) {
          reject(new Error('Failed to load image'))
          return
        }

        try {
          // Set default properties
          const defaultOptions = {
            id: `ai-image-${Date.now()}`,
            padding: 10,
            cornerSize: 10,
            imageSmoothing: true,
            strokeWidth: 0,
            stroke: null,
            selectable: true,
            evented: true,
            ...options
          }

          image.set(defaultOptions)

          // Auto-scale if image is too large
          const maxDimension = options.maxDimension || 400
          if (image.width > maxDimension || image.height > maxDimension) {
            const scale = Math.min(maxDimension / image.width, maxDimension / image.height)
            image.scale(scale)
          }

          // Position image (center by default or use provided position)
          if (!options.left && !options.top) {
            const canvasCenter = canvas.getCenter()
            image.set({
              left: canvasCenter.left,
              top: canvasCenter.top,
              originX: 'center',
              originY: 'center'
            })
          }

          // Add to canvas
          canvas.add(image)
          canvas.setActiveObject(image)
          canvas.renderAll()

          // Successfully added image to canvas
          resolve(image)
        } catch (error) {
          console.error('Error setting up image:', error)
          reject(error)
        }
      }, {
        crossOrigin: 'anonymous'
      })
    })
  } catch (error) {
    console.error("Error adding AI image to canvas:", error)
    throw new Error(`Failed to add image to canvas: ${error.message}`)
  }
}

// Replace selected object with AI-generated image
export const replaceSelectedWithImage = async (canvas, imageUrl, options = {}) => {
  if (!canvas) {
    console.error('Canvas is required for replaceSelectedWithImage')
    return null
  }

  if (!imageUrl) {
    console.error('Image URL is required for replaceSelectedWithImage')
    return null
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject) {
    console.warn('No object selected')
    return null
  }

  try {
    // Get position and size of selected object
    const objectBounds = {
      left: activeObject.left,
      top: activeObject.top,
      width: activeObject.width * activeObject.scaleX,
      height: activeObject.height * activeObject.scaleY,
      angle: activeObject.angle
    }

    // Remove selected object
    canvas.remove(activeObject)

    // Add new image at the same position
    const image = await addImageToCanvas(canvas, imageUrl, {
      ...options,
      left: objectBounds.left,
      top: objectBounds.top,
      maxDimension: Math.max(objectBounds.width, objectBounds.height)
    })

    // Apply similar transformations
    if (objectBounds.angle) {
      image.set('angle', objectBounds.angle)
    }

    canvas.renderAll()
    return image
  } catch (error) {
    console.error("Error replacing object with AI image:", error)
    throw error
  }
}

// Apply AI-processed image to selected object
export const applyProcessedImageToSelected = async (canvas, processedImageUrl) => {
  if (!canvas) {
    console.error('Canvas is required for applyProcessedImageToSelected')
    return null
  }

  if (!processedImageUrl) {
    console.error('Processed image URL is required')
    return null
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject || activeObject.type !== 'image') {
    console.warn('Please select an image object')
    return null
  }

  try {
    // Apply processed image to selected object

    // Use Promise-based approach with fabric.Image.fromURL
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(processedImageUrl, (newImage) => {
        if (!newImage) {
          reject(new Error('Failed to load processed image'))
          return
        }

        try {
          // Get current object properties
          const currentProps = {
            left: activeObject.left,
            top: activeObject.top,
            scaleX: activeObject.scaleX,
            scaleY: activeObject.scaleY,
            angle: activeObject.angle,
            opacity: activeObject.opacity,
            flipX: activeObject.flipX,
            flipY: activeObject.flipY,
            skewX: activeObject.skewX,
            skewY: activeObject.skewY
          }

          // Apply current properties to new image
          newImage.set(currentProps)
          newImage.set({
            id: activeObject.id || `processed-image-${Date.now()}`,
            padding: activeObject.padding || 10,
            cornerSize: activeObject.cornerSize || 10
          })

          // Replace the object
          canvas.remove(activeObject)
          canvas.add(newImage)
          canvas.setActiveObject(newImage)
          canvas.renderAll()

          // Successfully applied processed image
          resolve(newImage)
        } catch (error) {
          console.error('Error setting up processed image:', error)
          reject(error)
        }
      }, {
        crossOrigin: 'anonymous'
      })
    })
  } catch (error) {
    console.error("Error applying processed image:", error)
    throw new Error(`Failed to apply processed image: ${error.message}`)
  }
}

// Create image from data URL (for client-side processed images)
export const addDataUrlImageToCanvas = async (canvas, dataUrl, options = {}) => {
  return addImageToCanvas(canvas, dataUrl, options)
}

// Get image data URL from selected object
export const getSelectedImageDataUrl = (canvas, format = 'png', quality = 1.0) => {
  if (!canvas) {
    console.warn('Canvas not provided to getSelectedImageDataUrl')
    return null
  }

  try {
    const activeObject = canvas.getActiveObject()
    if (!activeObject || activeObject.type !== 'image') {
      console.warn('No image object selected')
      return null
    }

    // Create temporary canvas for the selected object
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')

    // Set canvas size to object size
    const objectBounds = activeObject.getBoundingRect()
    tempCanvas.width = objectBounds.width
    tempCanvas.height = objectBounds.height

    // Get the image element
    const imageElement = activeObject._originalElement || activeObject._element
    if (!imageElement) {
      console.warn('Could not access image element')
      return null
    }

    // Draw the image to temporary canvas
    tempCtx.drawImage(
      imageElement,
      0, 0,
      imageElement.width, imageElement.height,
      0, 0,
      tempCanvas.width, tempCanvas.height
    )

    // Return data URL
    return tempCanvas.toDataURL(`image/${format}`, quality)
  } catch (error) {
    console.error("Error getting image data URL:", error)
    return null
  }
}

// Get selected image URL (if available)
export const getSelectedImageUrl = (canvas) => {
  if (!isCanvasAvailable(canvas)) {
    console.warn('Canvas not available for getSelectedImageUrl')
    return null
  }

  try {
    const activeObject = getActiveObject(canvas)
    if (!activeObject || activeObject.type !== 'image') {
      return null
    }

    // Try to get the original source URL
    const imageElement = activeObject._originalElement || activeObject._element
    return imageElement?.src || imageElement?.currentSrc || null
  } catch (error) {
    console.error("Error getting image URL:", error)
    return null
  }
}

// Batch add multiple AI images
export const addMultipleImagesToCanvas = async (canvas, imageUrls, options = {}) => {
  if (!canvas || !Array.isArray(imageUrls)) {
    throw new Error('Canvas and image URLs array are required')
  }

  const results = []
  const spacing = options.spacing || 50
  const startX = options.startX || 100
  const startY = options.startY || 100

  for (let i = 0; i < imageUrls.length; i++) {
    try {
      const imageOptions = {
        ...options,
        left: startX + (i * spacing),
        top: startY + (Math.floor(i / 5) * spacing), // Arrange in rows of 5
        maxDimension: options.maxDimension || 200
      }

      const image = await addImageToCanvas(canvas, imageUrls[i], imageOptions)
      results.push(image)
    } catch (error) {
      console.error(`Failed to add image ${i}:`, error)
      results.push(null)
    }
  }

  return results
}

// Utility to check if object is an AI-generated image
export const isAiGeneratedImage = (object) => {
  return object && 
         object.type === 'image' && 
         (object.id?.includes('ai-image') || object.id?.includes('processed-image'))
}

// Get all AI images on canvas
export const getAiImagesOnCanvas = (canvas) => {
  if (!canvas) return []
  
  return canvas.getObjects().filter(isAiGeneratedImage)
}

// Remove all AI images from canvas
export const removeAllAiImages = (canvas) => {
  if (!canvas) return
  
  const aiImages = getAiImagesOnCanvas(canvas)
  aiImages.forEach(image => canvas.remove(image))
  canvas.renderAll()
  
  return aiImages.length
}
