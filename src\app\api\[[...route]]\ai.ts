import { z } from "zod";
import { <PERSON>o } from "hono";
import { verifyAuth } from "@hono/auth-js";
import { zValidator } from "@hono/zod-validator";

import {
  generateImage,
  removeBackground,
  upscaleImage,
  generateImageVariations,
  applyStyleFilter,
  getAvailableModels,
  getStyleFilters,
  isApiConfigured,
  AI_PROVIDERS
} from "@/services/aiService";

const app = new Hono()
  .get(
    "/status",
    verifyAuth(),
    async (c) => {
      try {
        const status = {
          together: isApiConfigured(AI_PROVIDERS.TOGETHER),
          fal: isApiConfigured(AI_PROVIDERS.FAL),
          replicate: isApiConfigured(AI_PROVIDERS.REPLICATE),
          clipdrop: isApiConfigured(AI_PROVIDERS.CLIPDROP),
          anyConfigured: isApiConfigured()
        };

        // API status checked successfully

        return c.json({ data: status });
      } catch (error) {
        console.error('API status check error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .post(
    "/remove-bg",
    verifyAuth(),
    zValidator(
      "json",
      z.object({
        image: z.string(),
        provider: z.enum(['fal', 'replicate', 'clipdrop', 'together']).optional(),
      }),
    ),
    async (c) => {
      try {
        const { image, provider } = c.req.valid("json");

        const result = await removeBackground(image, provider);

        return c.json({ data: result });
      } catch (error) {
        console.error('Background removal error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .post(
    "/generate-image",
    verifyAuth(),
    zValidator(
      "json",
      z.object({
        prompt: z.string(),
        width: z.number().optional(),
        height: z.number().optional(),
        steps: z.number().optional(),
        guidance: z.number().optional(),
      }),
    ),
    async (c) => {
      try {
        const { prompt, width, height, steps, guidance } = c.req.valid("json");

        const result = await generateImage(prompt, {
          width: width || 1024,
          height: height || 1024,
          steps: steps || 28,
          guidance: guidance || 7.5
        });

        return c.json({ data: result });
      } catch (error) {
        console.error('Image generation error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .post(
    "/upscale-image",
    verifyAuth(),
    zValidator(
      "json",
      z.object({
        image: z.string(),
        scale: z.number().min(1).max(4).optional(),
        provider: z.enum(['fal', 'replicate', 'clipdrop']).optional(),
      }),
    ),
    async (c) => {
      try {
        const { image, scale, provider } = c.req.valid("json");

        const result = await upscaleImage(image, scale || 2, provider);

        return c.json({ data: result });
      } catch (error) {
        console.error('Image upscaling error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .post(
    "/generate-variations",
    verifyAuth(),
    zValidator(
      "json",
      z.object({
        image: z.string(),
        prompt: z.string().optional(),
        width: z.number().optional(),
        height: z.number().optional(),
        steps: z.number().optional(),
        guidance: z.number().optional(),
      }),
    ),
    async (c) => {
      try {
        const { image, prompt, width, height, steps, guidance } = c.req.valid("json");

        const result = await generateImageVariations(image, prompt, {
          width: width || 1024,
          height: height || 1024,
          steps: steps || 28,
          guidance: guidance || 7.5
        });

        return c.json({ data: result });
      } catch (error) {
        console.error('Image variations error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .post(
    "/apply-style-filter",
    verifyAuth(),
    zValidator(
      "json",
      z.object({
        image: z.string(),
        style: z.string(),
        customPrompt: z.string().optional(),
      }),
    ),
    async (c) => {
      try {
        const { image, style, customPrompt } = c.req.valid("json");

        const result = await applyStyleFilter(image, style, customPrompt);

        return c.json({ data: result });
      } catch (error) {
        console.error('Style filter error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .get(
    "/models",
    verifyAuth(),
    async (c) => {
      try {
        const category = c.req.query('category');
        const models = getAvailableModels(category);
        return c.json({ data: models });
      } catch (error) {
        console.error('Get models error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .get(
    "/style-filters",
    verifyAuth(),
    async (c) => {
      try {
        const filters = getStyleFilters();
        return c.json({ data: filters });
      } catch (error) {
        console.error('Get style filters error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  )
  .get(
    "/status",
    verifyAuth(),
    async (c) => {
      try {
        const status = {
          together: isApiConfigured(AI_PROVIDERS.TOGETHER),
          fal: isApiConfigured(AI_PROVIDERS.FAL),
          replicate: isApiConfigured(AI_PROVIDERS.REPLICATE),
          anyConfigured: isApiConfigured()
        };
        return c.json({ data: status });
      } catch (error) {
        console.error('Get status error:', error);
        return c.json({ error: error instanceof Error ? error.message : 'Unknown error' }, 500);
      }
    },
  );

export default app;
