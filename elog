hook.js:608 Warning: Extra attributes from the server: webcrx Error Component Stack
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
warnForExtraAttributes @ react-dom.development.js:32731
diffHydratedProperties @ react-dom.development.js:35117
hydrateInstance @ react-dom.development.js:36127
prepareToHydrateHostInstance @ react-dom.development.js:7246
completeWork @ react-dom.development.js:19725
completeUnitOfWork @ react-dom.development.js:25963
performUnitOfWork @ react-dom.development.js:25759
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performConcurrentWorkOnRoot @ react-dom.development.js:24504
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:26.847Z'}
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: false
use-editor.ts:894 🎨 [TEMPLATE EDITOR] No canvas available, returning undefined editor
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: false
use-editor.ts:894 🎨 [TEMPLATE EDITOR] No canvas available, returning undefined editor
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: false, activeTool: 'select', canvasReady: false, timestamp: '2025-07-26T06:14:26.852Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:26.854Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: false, activeTool: 'select', canvasReady: false, timestamp: '2025-07-26T06:14:26.855Z'}
hook.js:608 Warning: Chrome: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at EditorComponent (editor.tsx:42:28)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
hook.js:608 Warning: Checkboard: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Checkboard (Checkboard.js:16:20)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at Chrome (Chrome.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at EditorComponent (editor.tsx:42:28)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
hook.js:608 Warning: Circle: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at EditorComponent (editor.tsx:42:28)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
hook.js:608 Warning: CircleSwatch: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead. Error Component Stack
    at CircleSwatch (CircleSwatch.js:15:20)
    at span (<anonymous>)
    at Hover (hover.js:33:7)
    at div (<anonymous>)
    at Circle (Circle.js:26:20)
    at ColorPicker (ColorWrap.js:28:7)
    at div (<anonymous>)
    at ColorPicker (color-picker.tsx:12:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at eval (index.mjs:116:13)
    at div (<anonymous>)
    at eval (index.mjs:36:13)
    at Provider (index.mjs:34:15)
    at eval (index.mjs:55:13)
    at _c (scroll-area.tsx:11:6)
    at aside (<anonymous>)
    at FillColorSidebar (fill-color-sidebar.tsx:16:3)
    at div (<anonymous>)
    at div (<anonymous>)
    at EditorComponent (editor.tsx:42:28)
    at EditorProjectIdPage (page.tsx:19:3)
    at ClientPageRoot (client-page.js:14:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at InnerLayoutRouter (layout-router.js:243:11)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at LoadingBoundary (layout-router.js:349:11)
    at ErrorBoundary (error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (layout-router.js:153:9)
    at ScrollAndFocusHandler (layout-router.js:228:11)
    at RenderFromTemplateContext (render-from-template-context.js:16:44)
    at OuterLayoutRouter (layout-router.js:370:11)
    at QueryClientProvider (QueryClientProvider.js:27:11)
    at QueryProvider (query-provider.tsx:43:33)
    at Providers (providers.tsx:9:29)
    at body (<anonymous>)
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
validateFunctionComponentInDev @ react-dom.development.js:16978
mountIndeterminateComponent @ react-dom.development.js:16941
beginWork$1 @ react-dom.development.js:18458
beginWork @ react-dom.development.js:26927
performUnitOfWork @ react-dom.development.js:25748
workLoopSync @ react-dom.development.js:25464
renderRootSync @ react-dom.development.js:25419
performSyncWorkOnRoot @ react-dom.development.js:24887
flushSyncWorkAcrossRoots_impl @ react-dom.development.js:7758
flushSyncWorkOnAllRoots @ react-dom.development.js:7718
processRootScheduleInMicrotask @ react-dom.development.js:7863
eval @ react-dom.development.js:8034
setTimeout
scheduleFn @ notifyManager.js:16
flush @ notifyManager.js:53
batch @ notifyManager.js:28
#dispatch @ query.js:341
setData @ query.js:52
onSuccess @ query.js:244
resolve @ retryer.js:58
Promise.then
run @ retryer.js:97
start @ retryer.js:134
fetch @ query.js:271
#executeFetch @ queryObserver.js:172
onSubscribe @ queryObserver.js:48
subscribe @ subscribable.js:13
eval @ useBaseQuery.js:41
subscribeToStore @ react-dom.development.js:12033
commitHookEffectListMount @ react-dom.development.js:21102
commitHookPassiveMountEffects @ react-dom.development.js:23154
commitPassiveMountOnFiber @ react-dom.development.js:23259
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23256
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23370
recursivelyTraversePassiveMountEffects @ react-dom.development.js:23237
commitPassiveMountOnFiber @ react-dom.development.js:23267
commitPassiveMountEffects @ react-dom.development.js:23225
flushPassiveEffectsImpl @ react-dom.development.js:26497
flushPassiveEffects @ react-dom.development.js:26438
eval @ react-dom.development.js:26172
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
editor.tsx:47 🎨 [TEMPLATE EDITOR] Component mounted with initial data: {projectId: 'e2ed1318-3d4e-4502-8a30-e9d62ee92112', projectName: 'poster', width: 900, height: 1200, hasJson: true, …}
editor.tsx:64 🎨 [TEMPLATE EDITOR] Initial JSON structure: {version: '5.3.0', objectCount: 8, objects: Array(8)}
use-editor.ts:785 🎨 [TEMPLATE EDITOR] Canvas state cleared in useEditor
use-editor.ts:798 🎨 [TEMPLATE EDITOR] Container state cleared in useEditor
editor.tsx:287 🎨 [TEMPLATE EDITOR] Canvas initialization effect triggered
editor.tsx:344 🎨 [TEMPLATE EDITOR] Starting immediate canvas initialization
editor.tsx:296 🎨 [TEMPLATE EDITOR] Attempting canvas initialization...
editor.tsx:308 🎨 [TEMPLATE EDITOR] Container dimensions: {width: 1307, height: 835, timestamp: '2025-07-26T06:14:27.465Z'}
editor.tsx:320 🎨 [TEMPLATE EDITOR] Creating fabric canvas...
editor.tsx:326 🎨 [TEMPLATE EDITOR] Fabric canvas created, initializing editor...
use-editor.ts:923 🎨 [TEMPLATE EDITOR] Init function called with: {hasCanvas: true, hasContainer: true, containerWidth: 1307, containerHeight: 835, timestamp: '2025-07-26T06:14:27.476Z'}
use-editor.ts:941 🎨 [TEMPLATE EDITOR] Fabric.js object prototypes configured
use-editor.ts:954 🎨 [TEMPLATE EDITOR] Text baseline fixes applied
use-editor.ts:966 🎨 [TEMPLATE EDITOR] Render method override applied
use-editor.ts:974 🎨 [TEMPLATE EDITOR] Calculated dimensions: {containerWidth: 1307, containerHeight: 835, workspaceWidth: 900, workspaceHeight: 1200, timestamp: '2025-07-26T06:14:27.481Z'}
use-editor.ts:988 🎨 [TEMPLATE EDITOR] Creating initial workspace...
use-editor.ts:1002 🎨 [TEMPLATE EDITOR] Setting canvas dimensions...
use-editor.ts:1006 🎨 [TEMPLATE EDITOR] Adding workspace to canvas...
use-editor.ts:1011 🎨 [TEMPLATE EDITOR] Setting canvas and container state...
use-editor.ts:1015 🎨 [TEMPLATE EDITOR] Initializing canvas history...
use-editor.ts:1022 🎨 [TEMPLATE EDITOR] Init function completed successfully: {canvasWidth: 1307, canvasHeight: 835, workspaceWidth: 900, workspaceHeight: 1200, objectCount: 1, …}
editor.tsx:332 🎨 [TEMPLATE EDITOR] Canvas initialization completed successfully
editor.tsx:79 🎨 [TEMPLATE EDITOR] Component unmounting
editor.tsx:373 🎨 [TEMPLATE EDITOR] Disposing canvas on cleanup
editor.tsx:47 🎨 [TEMPLATE EDITOR] Component mounted with initial data: {projectId: 'e2ed1318-3d4e-4502-8a30-e9d62ee92112', projectName: 'poster', width: 900, height: 1200, hasJson: true, …}
editor.tsx:64 🎨 [TEMPLATE EDITOR] Initial JSON structure: {version: '5.3.0', objectCount: 8, objects: Array(8)}
use-editor.ts:785 🎨 [TEMPLATE EDITOR] Canvas state cleared in useEditor
use-editor.ts:798 🎨 [TEMPLATE EDITOR] Container state cleared in useEditor
editor.tsx:287 🎨 [TEMPLATE EDITOR] Canvas initialization effect triggered
editor.tsx:344 🎨 [TEMPLATE EDITOR] Starting immediate canvas initialization
editor.tsx:296 🎨 [TEMPLATE EDITOR] Attempting canvas initialization...
editor.tsx:308 🎨 [TEMPLATE EDITOR] Container dimensions: {width: 1307, height: 835, timestamp: '2025-07-26T06:14:27.548Z'}
editor.tsx:320 🎨 [TEMPLATE EDITOR] Creating fabric canvas...
editor.tsx:326 🎨 [TEMPLATE EDITOR] Fabric canvas created, initializing editor...
use-editor.ts:923 🎨 [TEMPLATE EDITOR] Init function called with: {hasCanvas: true, hasContainer: true, containerWidth: 1307, containerHeight: 835, timestamp: '2025-07-26T06:14:27.552Z'}
use-editor.ts:941 🎨 [TEMPLATE EDITOR] Fabric.js object prototypes configured
use-editor.ts:954 🎨 [TEMPLATE EDITOR] Text baseline fixes applied
use-editor.ts:966 🎨 [TEMPLATE EDITOR] Render method override applied
use-editor.ts:974 🎨 [TEMPLATE EDITOR] Calculated dimensions: {containerWidth: 1307, containerHeight: 835, workspaceWidth: 900, workspaceHeight: 1200, timestamp: '2025-07-26T06:14:27.553Z'}
use-editor.ts:988 🎨 [TEMPLATE EDITOR] Creating initial workspace...
use-editor.ts:1002 🎨 [TEMPLATE EDITOR] Setting canvas dimensions...
use-editor.ts:1006 🎨 [TEMPLATE EDITOR] Adding workspace to canvas...
use-editor.ts:1011 🎨 [TEMPLATE EDITOR] Setting canvas and container state...
use-editor.ts:1015 🎨 [TEMPLATE EDITOR] Initializing canvas history...
use-editor.ts:1022 🎨 [TEMPLATE EDITOR] Init function completed successfully: {canvasWidth: 1307, canvasHeight: 835, workspaceWidth: 900, workspaceHeight: 1200, objectCount: 1, …}
editor.tsx:332 🎨 [TEMPLATE EDITOR] Canvas initialization completed successfully
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:27.559Z'}
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 1, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 1, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:27.564Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:27.566Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:27.566Z'}
use-editor.ts:777 🎨 [TEMPLATE EDITOR] Canvas state updated in useEditor: {canvasId: 'no-id', width: 1307, height: 835, objectCount: 1, timestamp: '2025-07-26T06:14:27.813Z'}
use-editor.ts:792 🎨 [TEMPLATE EDITOR] Container state updated in useEditor: {width: 1307, height: 835, timestamp: '2025-07-26T06:14:27.816Z'}
use-load-state.ts:25 🎨 [TEMPLATE EDITOR] useLoadState: Attempting to load initial state
use-load-state.ts:33 🎨 [TEMPLATE EDITOR] useLoadState: Parsed JSON data: {objectCount: 8, version: '5.3.0', hasBackground: false, backgroundImage: undefined, timestamp: '2025-07-26T06:14:27.823Z'}
use-load-state.ts:43 🎨 [TEMPLATE EDITOR] useLoadState: Object details: (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
use-load-state.ts:60 🎨 [TEMPLATE EDITOR] useLoadState: Canvas dimensions: {canvasWidth: 1307, canvasHeight: 835, timestamp: '2025-07-26T06:14:27.826Z'}
use-load-state.ts:71 🎨 [TEMPLATE EDITOR] useLoadState: Loading JSON into canvas...
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 1, canUndo: false, canRedo: false, …}
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: {width: 1307, height: 835, zoom: 1, timestamp: '2025-07-26T06:14:27.928Z'}
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
use-load-state.ts:76 🎨 [TEMPLATE EDITOR] useLoadState: JSON loaded successfully: {objectsCount: 8, loadDurationMs: 484, timestamp: '2025-07-26T06:14:28.304Z'}
use-load-state.ts:129 🎨 [TEMPLATE EDITOR] useLoadState: Calling autoZoom...
use-auto-resize.ts:27 🎨 [TEMPLATE EDITOR] autoZoom called
use-auto-resize.ts:38 🎨 [TEMPLATE EDITOR] autoZoom: Container dimensions: {width: 1307, height: 835, timestamp: '2025-07-26T06:14:28.313Z'}
use-auto-resize.ts:55 🎨 [TEMPLATE EDITOR] autoZoom: Canvas center: {top: 417.5, left: 653.5}
use-auto-resize.ts:71 🎨 [TEMPLATE EDITOR] autoZoom: Workspace dimensions: {workspaceWidth: 900, workspaceHeight: 1200}
use-auto-resize.ts:89 🎨 [TEMPLATE EDITOR] autoZoom: Calculated zoom values: {scale: 0.6958333333333333, zoomRatio: 0.85, finalZoom: 0.5914583333333333}
use-auto-resize.ts:120 🎨 [TEMPLATE EDITOR] autoZoom: Workspace center: Point {x: 1207.5, y: 523.5}
use-auto-resize.ts:159 🎨 [TEMPLATE EDITOR] autoZoom completed successfully: {durationMs: 5, finalZoom: 0.5914583333333333, clampedZoom: 0.5914583333333333, canvasSize: {…}, viewportTransform: Array(6), …}
use-load-state.ts:176 🎨 [TEMPLATE EDITOR] useLoadState: Template loading complete: {totalLoadDurationMs: 484, autoZoomDurationMs: 8, finalObjectCount: 8, canvasSize: {…}, timestamp: '2025-07-26T06:14:28.320Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:28.416Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:28.417Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:28.420Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:28.421Z'}
use-auto-resize.ts:188 🎨 [TEMPLATE EDITOR] Final interaction verification completed
use-auto-resize.ts:191 🎨 [TEMPLATE EDITOR] ✅ PAGE FULLY LOADED AND READY FOR INTERACTION ✅
use-load-state.ts:170 🎨 [TEMPLATE EDITOR] Object interactivity verified after auto-zoom
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:91 🎨 [TEMPLATE EDITOR] Debounced save triggered: {width: 900, height: 1200, jsonLength: 724859, timestamp: '2025-07-26T06:14:33.289Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:33.310Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:33.310Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:33.311Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:33.312Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:33.687Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:33.689Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:33.691Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:33.692Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:33.971Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:33.972Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:33.973Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:33.975Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:34.601Z'}
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:34.604Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:34.606Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:34.606Z'}
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 8, canUndo: false, canRedo: false, …}
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: {width: 1307, height: 835, zoom: 0.5914583333333333, timestamp: '2025-07-26T06:14:34.809Z'}
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:225 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: {projectId: 'e2ed1318-3d4e-4502-8a30-e9d62ee92112', projectName: 'poster', canvasObjects: 8, canvasSize: {…}, zoom: 0.5914583333333333, …}
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:38.138Z'}
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:38.140Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:38.141Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:38.141Z'}
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 8, canUndo: false, canRedo: false, …}
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: {width: 1307, height: 835, zoom: 0.5914583333333333, timestamp: '2025-07-26T06:14:38.242Z'}
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:178 🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:38.265Z'}
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:38.267Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:38.268Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:38.268Z'}
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 8, canUndo: true, canRedo: false, …}
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: {width: 1307, height: 835, zoom: 0.5914583333333333, timestamp: '2025-07-26T06:14:38.384Z'}
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:38.852Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:38.852Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:38.853Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:38.853Z'}
editor.tsx:225 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: {projectId: 'e2ed1318-3d4e-4502-8a30-e9d62ee92112', projectName: 'poster', canvasObjects: 8, canvasSize: {…}, zoom: 0.5914583333333333, …}
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
editor.tsx:91 🎨 [TEMPLATE EDITOR] Debounced save triggered: {width: 900, height: 1200, jsonLength: 724858, timestamp: '2025-07-26T06:14:43.274Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:43.291Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:43.292Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:43.293Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:43.293Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:43.474Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:43.474Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724859, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:43.475Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:43.476Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724858, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:43.654Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:43.655Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724858, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:43.656Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:43.657Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724858, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:48.260Z'}
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
use-editor.ts:855 🎨 [TEMPLATE EDITOR] Editor useMemo triggered, canvas available: true
use-editor.ts:858 🎨 [TEMPLATE EDITOR] Building editor with canvas: {canvasWidth: 1307, canvasHeight: 835, objectCount: 8, canUndo: ƒ, canRedo: ƒ, …}
use-editor.ts:890 🎨 [TEMPLATE EDITOR] Editor built successfully
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:48.263Z'}
editor.tsx:111 🎨 [TEMPLATE EDITOR] Initializing editor hook...
use-editor.ts:758 🎨 [TEMPLATE EDITOR] useEditor hook called with: {hasDefaultState: true, defaultStateLength: 724858, defaultWidth: 900, defaultHeight: 1200, timestamp: '2025-07-26T06:14:48.264Z'}
editor.tsx:382 🎨 [TEMPLATE EDITOR] Component rendering with: {hasEditor: true, activeTool: 'select', canvasReady: true, timestamp: '2025-07-26T06:14:48.264Z'}
editor.tsx:197 🎨 [TEMPLATE EDITOR] Removing canvas event listeners
editor.tsx:153 🎨 [TEMPLATE EDITOR] Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 8, canUndo: true, canRedo: false, …}
editor.tsx:164 🎨 [TEMPLATE EDITOR] Canvas dimensions: {width: 1307, height: 835, zoom: 0.5914583333333333, timestamp: '2025-07-26T06:14:48.347Z'}
editor.tsx:188 🎨 [TEMPLATE EDITOR] Canvas event listeners attached
editor.tsx:209 🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail
editor.tsx:225 🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯: {projectId: 'e2ed1318-3d4e-4502-8a30-e9d62ee92112', projectName: 'poster', canvasObjects: 8, canvasSize: {…}, zoom: 0.5914583333333333, …}
editor.tsx:192 🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay
