// Image caching and performance utilities

// LRU Cache implementation
class ImageCache {
  constructor(maxSize = 50) {
    this.cache = new Map()
    this.maxSize = maxSize
  }

  set(key, value) {
    // Remove oldest entry if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    // Add new entry
    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      accessCount: 1
    })
  }

  get(key) {
    const entry = this.cache.get(key)
    if (entry) {
      // Move to end (LRU) and update access stats
      this.cache.delete(key)
      entry.accessCount++
      entry.lastAccessed = Date.now()
      this.cache.set(key, entry)
      return entry.data
    }
    return null
  }

  has(key) {
    return this.cache.has(key)
  }

  delete(key) {
    return this.cache.delete(key)
  }

  clear() {
    this.cache.clear()
  }

  size() {
    return this.cache.size
  }

  // Get cache statistics
  getStats() {
    const entries = Array.from(this.cache.values())
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      totalAccesses: entries.reduce((sum, entry) => sum + entry.accessCount, 0),
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : null,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : null
    }
  }

  // Clean expired entries
  cleanup(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    const now = Date.now()
    const keysToDelete = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > maxAge) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    return keysToDelete.length
  }
}

// Global image cache instance
export const imageCache = new ImageCache(100)

// Generate cache key for AI requests
export function generateCacheKey(type, params) {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key]
      return result
    }, {})
  
  return `${type}:${JSON.stringify(sortedParams)}`
}

// Cached AI service functions
export async function cachedGenerateImage(generateImageFn, prompt, options = {}) {
  const cacheKey = generateCacheKey('generate', { prompt, ...options })
  
  if (imageCache.has(cacheKey)) {
    console.log('Cache hit for image generation:', cacheKey)
    return imageCache.get(cacheKey)
  }
  
  console.log('Cache miss for image generation:', cacheKey)
  const result = await generateImageFn(prompt, options)
  imageCache.set(cacheKey, result)
  
  return result
}

export async function cachedRemoveBackground(removeBackgroundFn, imageUrl, provider) {
  const cacheKey = generateCacheKey('remove-bg', { imageUrl, provider })
  
  if (imageCache.has(cacheKey)) {
    console.log('Cache hit for background removal:', cacheKey)
    return imageCache.get(cacheKey)
  }
  
  console.log('Cache miss for background removal:', cacheKey)
  const result = await removeBackgroundFn(imageUrl, provider)
  imageCache.set(cacheKey, result)
  
  return result
}

export async function cachedUpscaleImage(upscaleImageFn, imageUrl, scale, provider) {
  const cacheKey = generateCacheKey('upscale', { imageUrl, scale, provider })
  
  if (imageCache.has(cacheKey)) {
    console.log('Cache hit for image upscaling:', cacheKey)
    return imageCache.get(cacheKey)
  }
  
  console.log('Cache miss for image upscaling:', cacheKey)
  const result = await upscaleImageFn(imageUrl, scale, provider)
  imageCache.set(cacheKey, result)
  
  return result
}

// Image preloading utilities
export function preloadImage(src) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
  })
}

export async function preloadImages(urls) {
  const promises = urls.map(url => preloadImage(url).catch(err => ({ error: err, url })))
  return Promise.all(promises)
}

// Image optimization utilities
export function getOptimalImageSize(originalWidth, originalHeight, maxWidth = 2048, maxHeight = 2048) {
  if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
    return { width: originalWidth, height: originalHeight, scale: 1 }
  }
  
  const scaleX = maxWidth / originalWidth
  const scaleY = maxHeight / originalHeight
  const scale = Math.min(scaleX, scaleY)
  
  return {
    width: Math.round(originalWidth * scale),
    height: Math.round(originalHeight * scale),
    scale
  }
}

export function shouldOptimizeImage(width, height, maxDimension = 2048) {
  return width > maxDimension || height > maxDimension
}

// Performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
  }
  
  startTimer(operation) {
    const startTime = performance.now()
    return {
      end: () => {
        const duration = performance.now() - startTime
        this.recordMetric(operation, duration)
        return duration
      }
    }
  }
  
  recordMetric(operation, duration) {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, [])
    }
    
    const metrics = this.metrics.get(operation)
    metrics.push({
      duration,
      timestamp: Date.now()
    })
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift()
    }
  }
  
  getMetrics(operation) {
    const metrics = this.metrics.get(operation) || []
    if (metrics.length === 0) return null
    
    const durations = metrics.map(m => m.duration)
    const sum = durations.reduce((a, b) => a + b, 0)
    
    return {
      count: metrics.length,
      average: sum / metrics.length,
      min: Math.min(...durations),
      max: Math.max(...durations),
      recent: durations.slice(-10) // Last 10 measurements
    }
  }
  
  getAllMetrics() {
    const result = {}
    for (const [operation, _] of this.metrics) {
      result[operation] = this.getMetrics(operation)
    }
    return result
  }
  
  clear() {
    this.metrics.clear()
  }
}

export const performanceMonitor = new PerformanceMonitor()

// Debounced function utility
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Throttled function utility
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Memory usage monitoring
export function getMemoryUsage() {
  if (typeof window !== 'undefined' && 'memory' in performance) {
    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit,
      percentage: (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100
    }
  }
  return null
}

// Cleanup utilities
export function cleanupImageCache(maxAge = 24 * 60 * 60 * 1000) {
  return imageCache.cleanup(maxAge)
}

export function cleanupPerformanceMetrics() {
  performanceMonitor.clear()
}

// Auto cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    cleanupImageCache()
    cleanupPerformanceMetrics()
  })
  
  // Periodic cleanup every hour
  setInterval(() => {
    cleanupImageCache()
  }, 60 * 60 * 1000)
}
