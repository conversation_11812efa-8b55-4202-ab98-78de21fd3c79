import { useCallback, useRef } from "react";
import debounce from "lodash.debounce";
import { useQueryClient } from "@tanstack/react-query";

import { Editor } from "@/features/editor/types";
import { client } from "@/lib/hono";

interface UseThumbnailGeneratorProps {
  editor: Editor | undefined;
  projectId: string;
  onThumbnailGenerated?: (thumbnailUrl: string) => void;
}

export const useThumbnailGenerator = ({
  editor,
  projectId,
  onThumbnailGenerated,
}: UseThumbnailGeneratorProps) => {
  const queryClient = useQueryClient();
  const lastThumbnailRef = useRef<string | null>(null);
  const isGeneratingRef = useRef(false);
  const lastGenerationTimeRef = useRef(0);
  const MIN_GENERATION_INTERVAL = 10000; // Minimum 10 seconds between generations

  const generateThumbnail = useCallback(async () => {
    if (!editor) {
      console.warn("No editor available for thumbnail generation");
      return;
    }

    if (!editor.canvas) {
      console.warn("No canvas available for thumbnail generation");
      return;
    }

    // Throttling: prevent too frequent generation
    const now = Date.now();
    if (isGeneratingRef.current) {
      return;
    }

    if (now - lastGenerationTimeRef.current < MIN_GENERATION_INTERVAL) {
      return;
    }

    isGeneratingRef.current = true;
    lastGenerationTimeRef.current = now;

    try {
      // Generate thumbnail data URL with optimized settings
      const thumbnailDataUrl = editor.generateThumbnail({
        width: 300,
        height: 200,
        quality: 0.6,
        format: "image/jpeg",
      });

      if (!thumbnailDataUrl) {
        console.error("Failed to generate thumbnail data URL");
        return;
      }

      // Skip if thumbnail hasn't changed significantly
      if (lastThumbnailRef.current === thumbnailDataUrl) {
        return;
      }

      lastThumbnailRef.current = thumbnailDataUrl;

      // Upload thumbnail to external storage instead of storing base64 in database

      try {
        // Convert data URL to blob
        const response = await fetch(thumbnailDataUrl);
        const blob = await response.blob();

        // Create form data for upload
        const formData = new FormData();
        formData.append("file", blob, `thumbnail-${projectId}.jpg`);

        // Upload using simple upload API
        const uploadResponse = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.status}`);
        }

        const uploadResult = await uploadResponse.json();
        const thumbnailUrl = uploadResult.url;

        if (!thumbnailUrl) {
          throw new Error("No URL returned from upload");
        }

        // Update project with thumbnail URL (not data URL)
        const patchResponse = await client.api.projects[":id"].$patch({
          param: { id: projectId },
          json: {
            thumbnailUrl: thumbnailUrl,
          },
        });

        if (patchResponse.ok) {
          // Invalidate projects query to refresh the UI
          queryClient.invalidateQueries({ queryKey: ["projects"] });
          queryClient.invalidateQueries({ queryKey: ["project", { id: projectId }] });
          queryClient.refetchQueries({ queryKey: ["projects"] });

          onThumbnailGenerated?.(thumbnailUrl);
        } else {
          console.error("Failed to update project with thumbnail URL:", patchResponse.status, patchResponse.statusText);
        }
      } catch (uploadError) {
        console.error("Failed to upload thumbnail:", uploadError);
        // Fallback: store as data URL if upload fails
        const fallbackResponse = await client.api.projects[":id"].$patch({
          param: { id: projectId },
          json: {
            thumbnailUrl: thumbnailDataUrl,
          },
        });

        if (fallbackResponse.ok) {
          onThumbnailGenerated?.(thumbnailDataUrl);
        } else {
          console.error("Fallback thumbnail storage also failed");
        }
      }
    } catch (error) {
      console.error("Error generating thumbnail:", error);
    } finally {
      isGeneratingRef.current = false;
    }
  }, [editor, projectId, onThumbnailGenerated]);

  // Debounced version to avoid too frequent thumbnail generation
  const debouncedGenerateThumbnail = useCallback(
    debounce(generateThumbnail, 8000), // Generate thumbnail 8 seconds after last change
    [generateThumbnail]
  );

  const forceRegenerateThumbnail = useCallback(async () => {
    // Reset the last thumbnail to force regeneration
    lastThumbnailRef.current = null;
    // Reset throttling for forced regeneration
    lastGenerationTimeRef.current = 0;
    isGeneratingRef.current = false;
    await generateThumbnail();
  }, [generateThumbnail]);

  return {
    generateThumbnail,
    debouncedGenerateThumbnail,
    forceRegenerateThumbnail,
  };
};
