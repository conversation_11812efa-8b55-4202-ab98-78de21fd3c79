"use client";

import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Loader2, User, Calendar, Eye, Palette } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Image from "next/image";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface PublicProject {
  id: string;
  name: string;
  width: number;
  height: number;
  thumbnailUrl: string | null;
  isCustomizable: boolean;
  editableLayers: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    name: string | null;
    image: string | null;
  } | null;
}

interface PublicProjectsResponse {
  data: PublicProject[];
  nextPage: number | null;
}

const fetchPublicProjects = async (): Promise<PublicProject[]> => {
  console.log('fetchPublicProjects called');

  const response = await fetch(`/api/projects/public?page=1&limit=12`);
  console.log('API response status:', response.status, response.ok);

  if (!response.ok) {
    throw new Error('Failed to fetch public projects');
  }

  const result = await response.json();
  console.log('API response data:', result);

  return result.data;
};

export default function HomePage() {
  const {
    data: allProjects = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['homepage-public-projects'],
    queryFn: fetchPublicProjects,
    retry: 1,
    staleTime: 0,
  });

  // Debug logging - updated
  console.log('HomePage render (updated):', {
    isLoading,
    error: error?.message,
    allProjectsCount: allProjects.length,
    firstProject: allProjects[0]
  });

  // Add error logging
  if (error) {
    console.error('HomePage error:', error);
  }

  // Removed duplicate allProjects definition

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-yellow-600" />
          <p className="text-gray-600">Loading public gallery...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to load gallery</h2>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50">
      {/* Header */}
      <div className="bg-yellow-100/80 backdrop-blur-md border-b border-yellow-200/50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">Public Gallery</h1>
              <p className="text-gray-600 mt-2">
                Discover amazing designs created by our community
                {allProjects.length > 0 && (
                  <span className="ml-2 text-yellow-600 font-medium">
                    ({allProjects.length} design{allProjects.length !== 1 ? 's' : ''})
                  </span>
                )}
              </p>
            </div>
            <Link href="/dashboard">
              <Button variant="outline">
                Go to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        {allProjects.length === 0 ? (
          <div className="text-center py-12">
            <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No public projects yet</h2>
            <p className="text-gray-600">Be the first to share your design with the community!</p>
          </div>
        ) : (
          <>
            {/* Projects Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {allProjects.map((project) => (
                <Link key={project.id} href={project.isCustomizable && project.editableLayers ? `/public/${project.id}/customize` : `/public/${project.id}`}>
                  <Card className={`group hover:shadow-lg transition-all duration-200 cursor-pointer ${project.isCustomizable && project.editableLayers ? 'hover:ring-2 hover:ring-yellow-400 hover:ring-opacity-50' : ''}`}>
                    <CardContent className="p-0">
                    {/* Thumbnail */}
                    <div
                      className="relative w-full bg-white rounded-t-lg overflow-hidden"
                      style={{ aspectRatio: `${project.width}/${project.height}` }}
                    >
                      {project.thumbnailUrl ? (
                        <img
                          src={project.thumbnailUrl}
                          alt={project.name}
                          className="absolute inset-0 w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-gray-100">
                          <Eye className="h-8 w-8 text-gray-400" />
                        </div>
                      )}

                      {/* Customizable indicator */}
                      {project.isCustomizable && project.editableLayers && (
                        <div className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                          <Palette className="h-3 w-3" />
                          <span>Click to Edit</span>
                        </div>
                      )}
                    </div>

                    {/* Project Info */}
                    <div className="p-4 space-y-3">
                      <div>
                        <h3 className="font-semibold text-gray-900 group-hover:text-yellow-600 transition-colors line-clamp-1">
                          {project.name}
                        </h3>
                        <div className="flex items-center justify-between mt-2">
                          <Badge variant="secondary" className="text-xs">
                            {project.width} × {project.height}
                          </Badge>
                        </div>
                      </div>

                      {/* Author & Date */}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-2">
                          {project.user?.image ? (
                            <Image
                              src={project.user.image}
                              alt={project.user.name || 'User'}
                              width={20}
                              height={20}
                              className="rounded-full"
                            />
                          ) : (
                            <User className="h-4 w-4" />
                          )}
                          <span className="truncate max-w-[100px]">
                            {project.user?.name || 'Anonymous'}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span className="text-xs">
                            {formatDistanceToNow(new Date(project.createdAt), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                    </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>


          </>
        )}
      </div>
    </div>
  );
}
