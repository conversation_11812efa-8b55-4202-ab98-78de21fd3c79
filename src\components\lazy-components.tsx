/**
 * Lazy-loaded components for better code splitting and performance
 * Implements intelligent loading with fallbacks and error boundaries
 */

import React, { lazy, Suspense, ComponentType } from "react";
import { Loader2 } from "lucide-react";

// Loading fallback component
const LoadingFallback = ({ message = "Loading..." }: { message?: string }) => (
  <div className="flex items-center justify-center p-8">
    <div className="flex flex-col items-center space-y-3">
      <Loader2 className="h-8 w-8 animate-spin text-yellow-600" />
      <p className="text-sm text-gray-600">{message}</p>
    </div>
  </div>
);

// Error boundary for lazy components
class LazyErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const Fallback = this.props.fallback || (() => (
        <div className="p-8 text-center">
          <p className="text-red-600">Failed to load component</p>
          <button 
            onClick={() => this.setState({ hasError: false })}
            className="mt-2 px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Retry
          </button>
        </div>
      ));
      return <Fallback />;
    }

    return this.props.children;
  }
}

// Higher-order component for lazy loading with enhanced features
function withLazyLoading<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  loadingMessage?: string,
  errorFallback?: ComponentType
) {
  const LazyComponent = lazy(importFn);
  
  return function LazyWrapper(props: P) {
    return (
      <LazyErrorBoundary fallback={errorFallback}>
        <Suspense fallback={<LoadingFallback message={loadingMessage} />}>
          <LazyComponent {...props} />
        </Suspense>
      </LazyErrorBoundary>
    );
  };
}

// Lazy-loaded editor components
export const LazyEditor = withLazyLoading(
  () => import("@/features/editor/components/editor"),
  "Loading Editor..."
);

export const LazyCustomizationEditor = withLazyLoading(
  () => import("@/features/editor/components/customization-editor"),
  "Loading Customization Editor..."
);

// Lazy-loaded sidebar components
export const LazySidebar = withLazyLoading(
  () => import("@/features/editor/components/sidebar"),
  "Loading Tools..."
);

export const LazyToolbar = withLazyLoading(
  () => import("@/features/editor/components/toolbar"),
  "Loading Toolbar..."
);

// Lazy-loaded AI components
export const LazyAiPanel = withLazyLoading(
  () => import("@/components/editor/panels/AiPanel"),
  "Loading AI Tools..."
);

// Lazy-loaded template components
export const LazyTemplateCard = withLazyLoading(
  () => import("@/app/(dashboard)/template-card"),
  "Loading Templates..."
);

// Preload functions for critical components
export const preloadEditor = () => {
  import("@/features/editor/components/editor");
};

export const preloadCustomizationEditor = () => {
  import("@/features/editor/components/customization-editor");
};

export const preloadAiPanel = () => {
  import("@/components/editor/panels/AiPanel");
};

// Intelligent preloading based on user interaction
export function setupIntelligentPreloading() {
  // Preload editor when user hovers over edit buttons
  document.addEventListener('mouseover', (e) => {
    const target = e.target as HTMLElement;
    if (target.closest('[data-preload="editor"]')) {
      preloadEditor();
    }
    if (target.closest('[data-preload="customization"]')) {
      preloadCustomizationEditor();
    }
    if (target.closest('[data-preload="ai"]')) {
      preloadAiPanel();
    }
  });

  // Preload on route changes (for Next.js)
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
      // Preload commonly used components
      preloadEditor();
    });
  }
}

// Component size analyzer (development only)
export function analyzeComponentSize() {
  if (process.env.NODE_ENV === 'development') {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          console.log('Bundle analysis:', {
            loadTime: entry.loadEventEnd - entry.loadEventStart,
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
          });
        }
      }
    });
    
    observer.observe({ entryTypes: ['navigation'] });
  }
}

// Dynamic import helper with retry logic
export async function dynamicImportWithRetry<T>(
  importFn: () => Promise<T>,
  retries = 3,
  delay = 1000
): Promise<T> {
  for (let i = 0; i < retries; i++) {
    try {
      return await importFn();
    } catch (error) {
      if (i === retries - 1) throw error;
      
      console.warn(`Import failed, retrying in ${delay}ms...`, error);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }
  
  throw new Error('All import retries failed');
}

// Chunk loading optimization
export function optimizeChunkLoading() {
  if (typeof window !== 'undefined') {
    // Prefetch critical chunks
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = '/chunks/editor.js'; // Adjust based on your build output
    document.head.appendChild(link);
  }
}

// Initialize optimizations
if (typeof window !== 'undefined') {
  setupIntelligentPreloading();
  analyzeComponentSize();
  optimizeChunkLoading();
}
